<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <RootNamespace>HighCapital.AuthenticationService.Domain.UnitTests</RootNamespace>
        <AssemblyName>HighCapital.AuthenticationService.Domain.UnitTests</AssemblyName>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="nunit" />
        <PackageReference Include="NUnit.Analyzers">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NUnit3TestAdapter" />
        <PackageReference Include="coverlet.collector">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Shouldly" />
        <PackageReference Include="HighCapital.Core" />
        <PackageReference Include="FluentAssertions" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\Domain\Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="Domain">
        <HintPath>..\..\..\..\.nuget\packages\highcapital.core\1.7.0\lib\net9.0\Domain.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
