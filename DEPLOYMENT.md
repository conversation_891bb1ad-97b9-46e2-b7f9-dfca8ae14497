# Deployment Guide - Authentication Service

Este guia explica como configurar o deploy automático da aplicação no Google Kubernetes Engine (GKE).

## Configuração Inicial

### 1. Configurar Service Account no Google Cloud

1. Acesse o Google Cloud Console
2. Vá para IAM & Admin > Service Accounts
3. Crie uma nova Service Account com as seguintes permissões:
   - Kubernetes Engine Developer
   - Storage Admin
   - Container Registry Service Agent

4. Gere uma chave JSON para a Service Account

### 2. Configurar Secrets no GitHub

No seu repositório GitHub, vá para Settings > Secrets and variables > Actions e adicione:

- `GCP_SA_KEY`: Cole o conteúdo completo do arquivo JSON da Service Account

### 3. Estrutura dos Arquivos

```
├── .github/workflows/deploy.yml    # GitHub Action para CI/CD
├── k8s/
│   ├── deployment.yml              # Configuração do Deployment
│   └── service.yml                 # Configuração do Service
├── kustomization.yml               # Configuração do Kustomize
└── DEPLOYMENT.md                   # Este arquivo
```

## Como Funciona

### GitHub Action (deploy.yml)
- **Trigger**: Push para branches `main` ou `develop`
- **Processo**:
  1. Faz checkout do código
  2. Autentica no Google Cloud
  3. Constrói a imagem Docker
  4. Faz push para Google Container Registry
  5. Aplica os manifestos Kubernetes no cluster

### Kubernetes Manifests

#### Deployment (k8s/deployment.yml)
- **Replicas**: 1 (conforme solicitado)
- **Imagem**: `gcr.io/highcapital-470117/authentication-service:latest`
- **Porta**: 8080
- **Health Checks**: Configurados para `/health`
- **Resources**: Limits e requests definidos

#### Service (k8s/service.yml)
- **Tipo**: LoadBalancer (expõe a aplicação externamente)
- **Porta**: 80 (externa) → 8080 (container)

## Configurações do Cluster

- **Nome**: cluster-high-capital-dev
- **Região**: us-east1-b
- **Projeto**: highcapital-470117

## Monitoramento

Após o deploy, você pode monitorar a aplicação com:

```bash
# Ver status dos pods
kubectl get pods -l app=authentication-service

# Ver logs
kubectl logs -l app=authentication-service

# Ver serviços
kubectl get services

# Verificar deployment
kubectl get deployment authentication-service
```

## Troubleshooting

### Problemas Comuns

1. **Erro de autenticação**: Verifique se o secret `GCP_SA_KEY` está configurado corretamente
2. **Imagem não encontrada**: Verifique se o build da imagem foi bem-sucedido
3. **Pod não inicia**: Verifique os logs do pod para erros de aplicação

### Comandos Úteis

```bash
# Conectar ao cluster
gcloud container clusters get-credentials cluster-high-capital-dev --zone us-east1-b --project highcapital-470117

# Forçar redeploy
kubectl rollout restart deployment/authentication-service

# Ver eventos do cluster
kubectl get events --sort-by=.metadata.creationTimestamp
```

## Próximos Passos

1. Configure health checks na sua aplicação (.NET) no endpoint `/health`
2. Considere adicionar configurações de ambiente específicas para produção
3. Configure monitoramento e alertas
4. Implemente estratégias de rollback automático
