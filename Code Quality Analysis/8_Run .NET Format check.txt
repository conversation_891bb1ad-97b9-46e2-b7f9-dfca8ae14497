﻿2025-09-05T02:29:36.0069247Z ##[group]Run dotnet format --verify-no-changes --verbosity diagnostic
2025-09-05T02:29:36.0070192Z [36;1mdotnet format --verify-no-changes --verbosity diagnostic[0m
2025-09-05T02:29:36.0224695Z shell: /usr/bin/bash -e {0}
2025-09-05T02:29:36.0225120Z env:
2025-09-05T02:29:36.0225428Z   DOTNET_VERSION: 9.0.x
2025-09-05T02:29:36.0286371Z   DOTNET_ROOT: /usr/share/dotnet
2025-09-05T02:29:36.0286860Z ##[endgroup]
2025-09-05T02:29:36.4739051Z   The dotnet runtime version is '9.0.8'.
2025-09-05T02:29:36.5533946Z   Using MSBuild.exe located in '/usr/share/dotnet/sdk/9.0.304/'.
2025-09-05T02:29:36.5661350Z   Formatting code files in workspace '/home/<USER>/work/authentication-service/authentication-service/HighCapitalAuthenticationService.sln'.
2025-09-05T02:29:36.5669366Z   Loading workspace.
2025-09-05T02:29:39.1967026Z     Determining projects to restore...
2025-09-05T02:29:39.1967667Z   All projects are up-to-date for restore.
2025-09-05T02:29:45.2863707Z   Project Domain is using configuration from '/home/<USER>/work/authentication-service/authentication-service/artifacts/obj/Domain/debug/Domain.GeneratedMSBuildEditorConfig.editorconfig'.
2025-09-05T02:29:45.2868577Z   Project Domain is using configuration from '/usr/share/dotnet/sdk/9.0.304/Sdks/Microsoft.NET.Sdk/analyzers/build/config/analysislevel_9_default.globalconfig'.
2025-09-05T02:29:45.2872578Z   Project Application is using configuration from '/home/<USER>/work/authentication-service/authentication-service/artifacts/obj/Application/debug/Application.GeneratedMSBuildEditorConfig.editorconfig'.
2025-09-05T02:29:45.2876456Z   Project Application is using configuration from '/usr/share/dotnet/sdk/9.0.304/Sdks/Microsoft.NET.Sdk/analyzers/build/config/analysislevel_9_default.globalconfig'.
2025-09-05T02:29:45.2894633Z   Project Infrastructure is using configuration from '/home/<USER>/work/authentication-service/authentication-service/artifacts/obj/Infrastructure/debug/Infrastructure.GeneratedMSBuildEditorConfig.editorconfig'.
2025-09-05T02:29:45.2897422Z   Project Infrastructure is using configuration from '/usr/share/dotnet/sdk/9.0.304/Sdks/Microsoft.NET.Sdk/analyzers/build/config/analysislevel_9_default.globalconfig'.
2025-09-05T02:29:45.2900548Z   Project Application.UnitTests is using configuration from '/home/<USER>/work/authentication-service/authentication-service/artifacts/obj/Application.UnitTests/debug/Application.UnitTests.GeneratedMSBuildEditorConfig.editorconfig'.
2025-09-05T02:29:45.2903335Z   Project Application.UnitTests is using configuration from '/usr/share/dotnet/sdk/9.0.304/Sdks/Microsoft.NET.Sdk/analyzers/build/config/analysislevel_9_default.globalconfig'.
2025-09-05T02:29:45.2906065Z   Project Domain.UnitTests is using configuration from '/home/<USER>/work/authentication-service/authentication-service/artifacts/obj/Domain.UnitTests/debug/Domain.UnitTests.GeneratedMSBuildEditorConfig.editorconfig'.
2025-09-05T02:29:45.2911918Z   Project Domain.UnitTests is using configuration from '/usr/share/dotnet/sdk/9.0.304/Sdks/Microsoft.NET.Sdk/analyzers/build/config/analysislevel_9_default.globalconfig'.
2025-09-05T02:29:45.2914128Z   Project Api is using configuration from '/home/<USER>/work/authentication-service/authentication-service/artifacts/obj/Api/debug/Api.GeneratedMSBuildEditorConfig.editorconfig'.
2025-09-05T02:29:45.2916180Z   Project Api is using configuration from '/usr/share/dotnet/sdk/9.0.304/Sdks/Microsoft.NET.Sdk/analyzers/build/config/analysislevel_9_default.globalconfig'.
2025-09-05T02:29:45.2917345Z   Complete in 8717ms.
2025-09-05T02:29:45.2917740Z   Determining formattable files.
2025-09-05T02:29:45.5357663Z   Complete in 249ms.
2025-09-05T02:29:45.5360226Z   Running formatters.
2025-09-05T02:29:45.8418365Z /home/<USER>/work/authentication-service/authentication-service/src/Domain/Dtos/User.cs(8,12): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/src/Domain/Domain.csproj]
2025-09-05T02:29:45.8422492Z /home/<USER>/work/authentication-service/authentication-service/src/Domain/Dtos/User.cs(11,1): error WHITESPACE: Fix whitespace formatting. Replace 5 characters with '\n'. [/home/<USER>/work/authentication-service/authentication-service/src/Domain/Domain.csproj]
2025-09-05T02:29:45.8426641Z /home/<USER>/work/authentication-service/authentication-service/src/Domain/Dtos/User.cs(18,1): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Domain/Domain.csproj]
2025-09-05T02:29:45.8451265Z /home/<USER>/work/authentication-service/authentication-service/src/Domain/Dtos/User.cs(20,1): error WHITESPACE: Fix whitespace formatting. Replace 5 characters with '\n'. [/home/<USER>/work/authentication-service/authentication-service/src/Domain/Domain.csproj]
2025-09-05T02:29:45.8907047Z /home/<USER>/work/authentication-service/authentication-service/src/Application/DependencyInjection.cs(19,1): error WHITESPACE: Fix whitespace formatting. Replace 17 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9211665Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(59,30): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9216172Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(150,1): error WHITESPACE: Fix whitespace formatting. Replace 15 characters with '\n\s\s\s\s\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9220672Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(179,56): error WHITESPACE: Fix whitespace formatting. Insert '\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9228448Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(198,1): error WHITESPACE: Fix whitespace formatting. Replace 16 characters with '\n\s\s\s\s\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9232508Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(205,1): error WHITESPACE: Fix whitespace formatting. Replace 21 characters with '\n\s\s\s\s\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9236749Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(238,1): error WHITESPACE: Fix whitespace formatting. Replace 25 characters with '\n\s\s\s\s\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9241228Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(246,1): error WHITESPACE: Fix whitespace formatting. Replace 16 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9245357Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs(253,1): error WHITESPACE: Fix whitespace formatting. Replace 13 characters with '\n\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9440828Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Validators/UserValidators.cs(9,1): error WHITESPACE: Fix whitespace formatting. Replace 15 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9445991Z /home/<USER>/work/authentication-service/authentication-service/src/Application/Validators/UserValidators.cs(31,24): error WHITESPACE: Fix whitespace formatting. Insert '\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Application/Application.csproj]
2025-09-05T02:29:45.9450983Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Data/ApplicationDbContext.cs(16,39): error WHITESPACE: Fix whitespace formatting. Replace 10 characters with '\n\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9461991Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Data/ApplicationDbContext.cs(18,85): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9466393Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Data/ApplicationDbContext.cs(19,82): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9471115Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Data/ApplicationDbContext.cs(21,1): error WHITESPACE: Fix whitespace formatting. Replace 17 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9475885Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Data/ApplicationDbContext.cs(22,37): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9691219Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/ExternalServices/GoogleOAuthService.cs(38,1): error WHITESPACE: Fix whitespace formatting. Replace 16 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9695788Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Identity/IdentityService.cs(49,70): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9700416Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Identity/IdentityService.cs(92,5): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9704837Z /home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Identity/JwtTokenService.cs(27,1): error WHITESPACE: Fix whitespace formatting. Replace 15 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Infrastructure.csproj]
2025-09-05T02:29:45.9936775Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(35,37): error WHITESPACE: Fix whitespace formatting. Insert '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9944541Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(37,19): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9957528Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(38,22): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9962562Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(39,19): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9967547Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(41,1): error WHITESPACE: Fix whitespace formatting. Replace 17 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9972571Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(44,1): error WHITESPACE: Fix whitespace formatting. Replace 18 characters with '\n\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9977253Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(63,37): error WHITESPACE: Fix whitespace formatting. Insert '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9981936Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(65,19): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9986513Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(66,22): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9991317Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(67,19): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:45.9996524Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(72,1): error WHITESPACE: Fix whitespace formatting. Replace 17 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0001504Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(73,15): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0006172Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(86,37): error WHITESPACE: Fix whitespace formatting. Insert '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0011863Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(88,19): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0016654Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(89,22): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0021468Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(90,19): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0026177Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(95,1): error WHITESPACE: Fix whitespace formatting. Replace 17 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0044796Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(121,1): error WHITESPACE: Fix whitespace formatting. Replace 18 characters with '\n\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0047728Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(148,1): error WHITESPACE: Fix whitespace formatting. Replace 18 characters with '\n\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0052982Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(150,15): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0058271Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs(171,1): error WHITESPACE: Fix whitespace formatting. Replace 18 characters with '\n\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0063382Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(30,35): error WHITESPACE: Fix whitespace formatting. Replace 10 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0068656Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(31,10): error WHITESPACE: Fix whitespace formatting. Replace 14 characters with '\n\s\s\s\s\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0073879Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(127,1): error WHITESPACE: Fix whitespace formatting. Replace 17 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0079413Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(128,37): error WHITESPACE: Fix whitespace formatting. Insert '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0084127Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(130,19): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0089307Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(131,22): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0098773Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(132,19): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0103898Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(135,50): error WHITESPACE: Fix whitespace formatting. Insert '\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0112520Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(152,37): error WHITESPACE: Fix whitespace formatting. Insert '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0117824Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(154,19): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0123110Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(155,22): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0128487Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(156,19): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0133739Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs(160,50): error WHITESPACE: Fix whitespace formatting. Insert '\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0138886Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Services/UserServiceTests.cs(56,19): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0143939Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Services/UserServiceTests.cs(57,22): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0149335Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Services/UserServiceTests.cs(58,19): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0154155Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Services/UserServiceTests.cs(76,19): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0159380Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Services/UserServiceTests.cs(77,22): error WHITESPACE: Fix whitespace formatting. Delete 1 characters. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0164054Z /home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Services/UserServiceTests.cs(78,11): error WHITESPACE: Fix whitespace formatting. Insert '\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Application.UnitTests.csproj]
2025-09-05T02:29:46.0168615Z /home/<USER>/work/authentication-service/authentication-service/tests/Domain.UnitTests/Entities/UserTests.cs(14,31): error WHITESPACE: Fix whitespace formatting. Replace 10 characters with '\n\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Domain.UnitTests/Domain.UnitTests.csproj]
2025-09-05T02:29:46.0172946Z /home/<USER>/work/authentication-service/authentication-service/tests/Domain.UnitTests/Entities/UserTests.cs(17,1): error WHITESPACE: Fix whitespace formatting. Replace 15 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Domain.UnitTests/Domain.UnitTests.csproj]
2025-09-05T02:29:46.0177313Z /home/<USER>/work/authentication-service/authentication-service/tests/Domain.UnitTests/Entities/UserTests.cs(48,1): error WHITESPACE: Fix whitespace formatting. Replace 10 characters with '\n\n\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/tests/Domain.UnitTests/Domain.UnitTests.csproj]
2025-09-05T02:29:46.0228716Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(67,1): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0232909Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(70,5): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0236981Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(71,9): error WHITESPACE: Fix whitespace formatting. Delete 2 characters. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0241151Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(73,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0244853Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(75,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0324576Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(76,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0328689Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(78,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0332162Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(79,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0335641Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(80,13): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0339389Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(81,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0342836Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(83,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0346231Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(87,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0395282Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(88,9): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0398985Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(89,5): error WHITESPACE: Fix whitespace formatting. Insert '\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0402377Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(90,5): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0406000Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs(91,1): error WHITESPACE: Fix whitespace formatting. Delete 4 characters. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0409821Z /home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/UsersController.cs(54,60): error WHITESPACE: Fix whitespace formatting. Insert '\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0413197Z /home/<USER>/work/authentication-service/authentication-service/src/Api/DependencyInjection.cs(19,1): error WHITESPACE: Fix whitespace formatting. Replace 17 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.0416905Z /home/<USER>/work/authentication-service/authentication-service/src/Api/DependencyInjection.cs(20,17): error WHITESPACE: Fix whitespace formatting. Replace 9 characters with '\n\s\s\s\s\s\s\s\s'. [/home/<USER>/work/authentication-service/authentication-service/src/Api/Api.csproj]
2025-09-05T02:29:46.1540634Z   Running Code Style analysis.
2025-09-05T02:29:46.1548419Z   Determining diagnostics...
2025-09-05T02:29:48.0911779Z   Running 3 analyzers on Domain.
2025-09-05T02:29:48.7477281Z   Running 3 analyzers on Application.
2025-09-05T02:29:48.9963524Z   Running 3 analyzers on Infrastructure.
2025-09-05T02:29:49.1073136Z   Running 3 analyzers on Application.UnitTests.
2025-09-05T02:29:49.4628370Z   Running 3 analyzers on Domain.UnitTests.
2025-09-05T02:29:49.5382183Z   Running 3 analyzers on Api.
2025-09-05T02:29:49.6249348Z   Complete in 3470ms.
2025-09-05T02:29:49.6252009Z   Analysis complete in 3471ms.
2025-09-05T02:29:50.1475143Z   Running Analyzer Reference analysis.
2025-09-05T02:29:50.1477238Z   Determining diagnostics...
2025-09-05T02:29:50.2932101Z   Running 150 analyzers on Domain.
2025-09-05T02:29:50.5978881Z   Running 179 analyzers on Application.
2025-09-05T02:29:51.1008489Z   Running 179 analyzers on Infrastructure.
2025-09-05T02:29:51.6833724Z   Running 204 analyzers on Application.UnitTests.
2025-09-05T02:29:52.5935353Z   Running 204 analyzers on Domain.UnitTests.
2025-09-05T02:29:52.7556663Z   Running 184 analyzers on Api.
2025-09-05T02:29:53.0000032Z   Complete in 2852ms.
2025-09-05T02:29:53.0001860Z   Analysis complete in 2852ms.
2025-09-05T02:29:53.0003469Z   Complete in 7465ms.
2025-09-05T02:29:53.0005430Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Domain/Dtos/User.cs'.
2025-09-05T02:29:53.0008204Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Application/DependencyInjection.cs'.
2025-09-05T02:29:53.0010870Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Application/Services/UserService.cs'.
2025-09-05T02:29:53.0013868Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Application/Validators/UserValidators.cs'.
2025-09-05T02:29:53.0016674Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Data/ApplicationDbContext.cs'.
2025-09-05T02:29:53.0019889Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/ExternalServices/GoogleOAuthService.cs'.
2025-09-05T02:29:53.0022769Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Identity/IdentityService.cs'.
2025-09-05T02:29:53.0025706Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Infrastructure/Identity/JwtTokenService.cs'.
2025-09-05T02:29:53.0028786Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/AuthControllerTests.cs'.
2025-09-05T02:29:53.0031617Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Controllers/UsersControllerTests.cs'.
2025-09-05T02:29:53.0034458Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/tests/Application.UnitTests/Services/UserServiceTests.cs'.
2025-09-05T02:29:53.0037179Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/tests/Domain.UnitTests/Entities/UserTests.cs'.
2025-09-05T02:29:53.0040295Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/Auth/AuthController.cs'.
2025-09-05T02:29:53.0042918Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Api/Controllers/UsersController.cs'.
2025-09-05T02:29:53.0044518Z   Formatted code file '/home/<USER>/work/authentication-service/authentication-service/src/Api/DependencyInjection.cs'.
2025-09-05T02:29:53.0045524Z   Formatted 15 of 66 files.
2025-09-05T02:29:53.0046712Z   Format complete in 16431ms.
2025-09-05T02:29:53.0606253Z ##[error]Process completed with exit code 2.
