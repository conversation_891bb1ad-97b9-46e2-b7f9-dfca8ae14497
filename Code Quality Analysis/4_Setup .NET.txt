﻿2025-09-05T02:28:59.7232185Z ##[group]Run actions/setup-dotnet@v4
2025-09-05T02:28:59.7232561Z with:
2025-09-05T02:28:59.7232807Z   dotnet-version: 9.0.x
2025-09-05T02:28:59.7233097Z   cache: false
2025-09-05T02:28:59.7233341Z env:
2025-09-05T02:28:59.7233571Z   DOTNET_VERSION: 9.0.x
2025-09-05T02:28:59.7233991Z ##[endgroup]
2025-09-05T02:28:59.9097526Z [command]/home/<USER>/work/_actions/actions/setup-dotnet/v4/externals/install-dotnet.sh --skip-non-versioned-files --runtime dotnet --channel LTS
2025-09-05T02:29:00.2643831Z dotnet-install: Attempting to download using aka.ms link https://builds.dotnet.microsoft.com/dotnet/Runtime/8.0.19/dotnet-runtime-8.0.19-linux-x64.tar.gz
2025-09-05T02:29:00.5512056Z dotnet-install: Remote file https://builds.dotnet.microsoft.com/dotnet/Runtime/8.0.19/dotnet-runtime-8.0.19-linux-x64.tar.gz size is 31273338 bytes.
2025-09-05T02:29:00.5514248Z dotnet-install: Extracting archive from https://builds.dotnet.microsoft.com/dotnet/Runtime/8.0.19/dotnet-runtime-8.0.19-linux-x64.tar.gz
2025-09-05T02:29:01.2314179Z dotnet-install: Downloaded file size is 31273338 bytes.
2025-09-05T02:29:01.2314864Z dotnet-install: The remote and local file sizes are equal.
2025-09-05T02:29:01.2570981Z dotnet-install: Installed version is 8.0.19
2025-09-05T02:29:01.2619595Z dotnet-install: Adding to current process PATH: `/usr/share/dotnet`. Note: This change will be visible only when sourcing script.
2025-09-05T02:29:01.2622373Z dotnet-install: Note that the script does not resolve dependencies during installation.
2025-09-05T02:29:01.2624154Z dotnet-install: To check the list of dependencies, go to https://learn.microsoft.com/dotnet/core/install, select your operating system and check the "Dependencies" section.
2025-09-05T02:29:01.2625604Z dotnet-install: Installation finished successfully.
2025-09-05T02:29:01.2652802Z [command]/home/<USER>/work/_actions/actions/setup-dotnet/v4/externals/install-dotnet.sh --skip-non-versioned-files --channel 9.0
2025-09-05T02:29:01.5774260Z dotnet-install: Attempting to download using aka.ms link https://builds.dotnet.microsoft.com/dotnet/Sdk/9.0.304/dotnet-sdk-9.0.304-linux-x64.tar.gz
2025-09-05T02:29:02.7379454Z dotnet-install: Remote file https://builds.dotnet.microsoft.com/dotnet/Sdk/9.0.304/dotnet-sdk-9.0.304-linux-x64.tar.gz size is 217812954 bytes.
2025-09-05T02:29:02.7381664Z dotnet-install: Extracting archive from https://builds.dotnet.microsoft.com/dotnet/Sdk/9.0.304/dotnet-sdk-9.0.304-linux-x64.tar.gz
2025-09-05T02:29:07.7869031Z dotnet-install: Downloaded file size is 217812954 bytes.
2025-09-05T02:29:07.7870006Z dotnet-install: The remote and local file sizes are equal.
2025-09-05T02:29:09.9853827Z dotnet-install: Installed version is 9.0.304
2025-09-05T02:29:09.9917230Z dotnet-install: Adding to current process PATH: `/usr/share/dotnet`. Note: This change will be visible only when sourcing script.
2025-09-05T02:29:09.9922949Z dotnet-install: Note that the script does not resolve dependencies during installation.
2025-09-05T02:29:09.9924895Z dotnet-install: To check the list of dependencies, go to https://learn.microsoft.com/dotnet/core/install, select your operating system and check the "Dependencies" section.
2025-09-05T02:29:09.9927637Z dotnet-install: Installation finished successfully.
