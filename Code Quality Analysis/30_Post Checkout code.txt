﻿2025-09-05T02:29:53.0963902Z Post job cleanup.
2025-09-05T02:29:53.1914253Z [command]/usr/bin/git version
2025-09-05T02:29:53.1953422Z git version 2.51.0
2025-09-05T02:29:53.2010014Z Temporarily overriding HOME='/home/<USER>/work/_temp/e3887045-4546-4c80-9090-8fe4db167319' before making global git config changes
2025-09-05T02:29:53.2011988Z Adding repository directory to the temporary git global config as a safe directory
2025-09-05T02:29:53.2015803Z [command]/usr/bin/git config --global --add safe.directory /home/<USER>/work/authentication-service/authentication-service
2025-09-05T02:29:53.2055765Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
2025-09-05T02:29:53.2111750Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
2025-09-05T02:29:53.2465487Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
2025-09-05T02:29:53.2506620Z http.https://github.com/.extraheader
2025-09-05T02:29:53.2533363Z [command]/usr/bin/git config --local --unset-all http.https://github.com/.extraheader
2025-09-05T02:29:53.2590865Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
