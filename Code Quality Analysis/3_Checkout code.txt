﻿2025-09-05T02:28:59.0711125Z ##[group]Run actions/checkout@v4
2025-09-05T02:28:59.0711711Z with:
2025-09-05T02:28:59.0711946Z   fetch-depth: 0
2025-09-05T02:28:59.0712239Z   repository: HighCapitalTech/authentication-service
2025-09-05T02:28:59.0712694Z   token: ***
2025-09-05T02:28:59.0712896Z   ssh-strict: true
2025-09-05T02:28:59.0713108Z   ssh-user: git
2025-09-05T02:28:59.0713316Z   persist-credentials: true
2025-09-05T02:28:59.0713561Z   clean: true
2025-09-05T02:28:59.0713774Z   sparse-checkout-cone-mode: true
2025-09-05T02:28:59.0714050Z   fetch-tags: false
2025-09-05T02:28:59.0714267Z   show-progress: true
2025-09-05T02:28:59.0714503Z   lfs: false
2025-09-05T02:28:59.0714695Z   submodules: false
2025-09-05T02:28:59.0714912Z   set-safe-directory: true
2025-09-05T02:28:59.0715324Z env:
2025-09-05T02:28:59.0715557Z   DOTNET_VERSION: 9.0.x
2025-09-05T02:28:59.0715783Z ##[endgroup]
2025-09-05T02:28:59.1873185Z Syncing repository: HighCapitalTech/authentication-service
2025-09-05T02:28:59.1875024Z ##[group]Getting Git version info
2025-09-05T02:28:59.1875889Z Working directory is '/home/<USER>/work/authentication-service/authentication-service'
2025-09-05T02:28:59.1876943Z [command]/usr/bin/git version
2025-09-05T02:28:59.1902461Z git version 2.51.0
2025-09-05T02:28:59.1932672Z ##[endgroup]
2025-09-05T02:28:59.1946244Z Temporarily overriding HOME='/home/<USER>/work/_temp/8a457a90-2297-4109-a293-84852704ace4' before making global git config changes
2025-09-05T02:28:59.1949264Z Adding repository directory to the temporary git global config as a safe directory
2025-09-05T02:28:59.1953287Z [command]/usr/bin/git config --global --add safe.directory /home/<USER>/work/authentication-service/authentication-service
2025-09-05T02:28:59.1994457Z Deleting the contents of '/home/<USER>/work/authentication-service/authentication-service'
2025-09-05T02:28:59.1997000Z ##[group]Initializing the repository
2025-09-05T02:28:59.2002638Z [command]/usr/bin/git init /home/<USER>/work/authentication-service/authentication-service
2025-09-05T02:28:59.2114380Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-09-05T02:28:59.2116097Z hint: is subject to change. To configure the initial branch name to use in all
2025-09-05T02:28:59.2117014Z hint: of your new repositories, which will suppress this warning, call:
2025-09-05T02:28:59.2117805Z hint:
2025-09-05T02:28:59.2118559Z hint: 	git config --global init.defaultBranch <name>
2025-09-05T02:28:59.2119256Z hint:
2025-09-05T02:28:59.2119913Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-09-05T02:28:59.2121166Z hint: 'development'. The just-created branch can be renamed via this command:
2025-09-05T02:28:59.2122115Z hint:
2025-09-05T02:28:59.2122652Z hint: 	git branch -m <name>
2025-09-05T02:28:59.2123160Z hint:
2025-09-05T02:28:59.2125346Z hint: Disable this message with "git config set advice.defaultBranchName false"
2025-09-05T02:28:59.2126596Z Initialized empty Git repository in /home/<USER>/work/authentication-service/authentication-service/.git/
2025-09-05T02:28:59.2137269Z [command]/usr/bin/git remote add origin https://github.com/HighCapitalTech/authentication-service
2025-09-05T02:28:59.2176200Z ##[endgroup]
2025-09-05T02:28:59.2178749Z ##[group]Disabling automatic garbage collection
2025-09-05T02:28:59.2179522Z [command]/usr/bin/git config --local gc.auto 0
2025-09-05T02:28:59.2210142Z ##[endgroup]
2025-09-05T02:28:59.2210829Z ##[group]Setting up auth
2025-09-05T02:28:59.2217377Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
2025-09-05T02:28:59.2251246Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
2025-09-05T02:28:59.2585728Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
2025-09-05T02:28:59.2618198Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
2025-09-05T02:28:59.2857412Z [command]/usr/bin/git config --local http.https://github.com/.extraheader AUTHORIZATION: basic ***
2025-09-05T02:28:59.2900015Z ##[endgroup]
2025-09-05T02:28:59.2902238Z ##[group]Fetching the repository
2025-09-05T02:28:59.2910806Z [command]/usr/bin/git -c protocol.version=2 fetch --prune --no-recurse-submodules origin +refs/heads/*:refs/remotes/origin/* +refs/tags/*:refs/tags/* +da24850fd7a66a23efe52054545bb9f9b0e57a84:refs/remotes/pull/5/merge
2025-09-05T02:28:59.6608463Z From https://github.com/HighCapitalTech/authentication-service
2025-09-05T02:28:59.6626202Z  * [new branch]      feat/IntegrationWithHighCapitalCore      -> origin/feat/IntegrationWithHighCapitalCore
2025-09-05T02:28:59.6627519Z  * [new branch]      fix/ci-cd                                -> origin/fix/ci-cd
2025-09-05T02:28:59.6628588Z  * [new branch]      main                                     -> origin/main
2025-09-05T02:28:59.6629526Z  * [new branch]      release                                  -> origin/release
2025-09-05T02:28:59.6630440Z  * [new ref]         da24850fd7a66a23efe52054545bb9f9b0e57a84 -> pull/5/merge
2025-09-05T02:28:59.6669122Z ##[endgroup]
2025-09-05T02:28:59.6670178Z ##[group]Determining the checkout info
2025-09-05T02:28:59.6672037Z ##[endgroup]
2025-09-05T02:28:59.6678233Z [command]/usr/bin/git sparse-checkout disable
2025-09-05T02:28:59.6749058Z [command]/usr/bin/git config --local --unset-all extensions.worktreeConfig
2025-09-05T02:28:59.6794831Z ##[group]Checking out the ref
2025-09-05T02:28:59.6803114Z [command]/usr/bin/git checkout --progress --force refs/remotes/pull/5/merge
2025-09-05T02:28:59.6912243Z Note: switching to 'refs/remotes/pull/5/merge'.
2025-09-05T02:28:59.6912836Z 
2025-09-05T02:28:59.6913421Z You are in 'detached HEAD' state. You can look around, make experimental
2025-09-05T02:28:59.6914306Z changes and commit them, and you can discard any commits you make in this
2025-09-05T02:28:59.6915189Z state without impacting any branches by switching back to a branch.
2025-09-05T02:28:59.6916618Z 
2025-09-05T02:28:59.6918770Z If you want to create a new branch to retain commits you create, you may
2025-09-05T02:28:59.6921234Z do so (now or later) by using -c with the switch command. Example:
2025-09-05T02:28:59.6923439Z 
2025-09-05T02:28:59.6925346Z   git switch -c <new-branch-name>
2025-09-05T02:28:59.6927448Z 
2025-09-05T02:28:59.6928181Z Or undo this operation with:
2025-09-05T02:28:59.6929389Z 
2025-09-05T02:28:59.6930884Z   git switch -
2025-09-05T02:28:59.6932047Z 
2025-09-05T02:28:59.6933747Z Turn off this advice by setting config variable advice.detachedHead to false
2025-09-05T02:28:59.6935522Z 
2025-09-05T02:28:59.6936300Z HEAD is now at da24850 Merge 23664bb69af121c6408e894f26d0a77ee4ce8fee into 7dfad460ef720c6c3eead1eef70aead69eae66d0
2025-09-05T02:28:59.6949995Z ##[endgroup]
2025-09-05T02:28:59.6990616Z [command]/usr/bin/git log -1 --format=%H
2025-09-05T02:28:59.7014308Z da24850fd7a66a23efe52054545bb9f9b0e57a84
