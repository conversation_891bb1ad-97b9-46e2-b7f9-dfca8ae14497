﻿2025-09-05T02:29:23.5973993Z ##[group]Run dotnet build --configuration Release --no-restore
2025-09-05T02:29:23.5974608Z [36;1mdotnet build --configuration Release --no-restore[0m
2025-09-05T02:29:23.6005061Z shell: /usr/bin/bash -e {0}
2025-09-05T02:29:23.6005423Z env:
2025-09-05T02:29:23.6005693Z   DOTNET_VERSION: 9.0.x
2025-09-05T02:29:23.6006040Z   DOTNET_ROOT: /usr/share/dotnet
2025-09-05T02:29:23.6006398Z ##[endgroup]
2025-09-05T02:29:28.3890072Z   Domain -> /home/<USER>/work/authentication-service/authentication-service/artifacts/bin/Domain/release/HighCapital.AuthenticationService.Domain.dll
2025-09-05T02:29:29.9103438Z   Domain.UnitTests -> /home/<USER>/work/authentication-service/authentication-service/artifacts/bin/Domain.UnitTests/release/HighCapital.AuthenticationService.Domain.UnitTests.dll
2025-09-05T02:29:30.8156273Z   Application -> /home/<USER>/work/authentication-service/authentication-service/artifacts/bin/Application/release/HighCapital.AuthenticationService.Application.dll
2025-09-05T02:29:31.6213585Z   Infrastructure -> /home/<USER>/work/authentication-service/authentication-service/artifacts/bin/Infrastructure/release/HighCapital.AuthenticationService.Infrastructure.dll
2025-09-05T02:29:33.6181147Z   Api -> /home/<USER>/work/authentication-service/authentication-service/artifacts/bin/Api/release/HighCapital.AuthenticationService.Service.dll
2025-09-05T02:29:35.9006905Z   Application.UnitTests -> /home/<USER>/work/authentication-service/authentication-service/artifacts/bin/Application.UnitTests/release/HighCapital.AuthenticationService.Application.UnitTests.dll
2025-09-05T02:29:35.9338655Z 
2025-09-05T02:29:35.9339458Z Build succeeded.
2025-09-05T02:29:35.9339900Z     0 Warning(s)
2025-09-05T02:29:35.9340328Z     0 Error(s)
2025-09-05T02:29:35.9345567Z 
2025-09-05T02:29:35.9347120Z Time Elapsed 00:00:12.01
