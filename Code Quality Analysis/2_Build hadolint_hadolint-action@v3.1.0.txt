﻿2025-09-05T02:28:55.5899270Z ##[group]Build container for action use: '/home/<USER>/work/_actions/hadolint/hadolint-action/v3.1.0/Dockerfile'.
2025-09-05T02:28:55.5959964Z ##[command]/usr/bin/docker build -t f4a723:2b844dc1e4bd4f7e89a680704fb3083d -f "/home/<USER>/work/_actions/hadolint/hadolint-action/v3.1.0/Dockerfile" "/home/<USER>/work/_actions/hadolint/hadolint-action/v3.1.0"
2025-09-05T02:28:56.1897396Z #0 building with "default" instance using docker driver
2025-09-05T02:28:56.1924902Z 
2025-09-05T02:28:56.1925626Z #1 [internal] load build definition from Dockerfile
2025-09-05T02:28:56.1926425Z #1 transferring dockerfile: 221B done
2025-09-05T02:28:56.1926996Z #1 DONE 0.0s
2025-09-05T02:28:56.1927250Z 
2025-09-05T02:28:56.1928233Z #2 [internal] load metadata for ghcr.io/hadolint/hadolint:v2.12.0-debian
2025-09-05T02:28:56.2713852Z #2 DONE 0.2s
2025-09-05T02:28:56.3964590Z 
2025-09-05T02:28:56.3969505Z #3 [internal] load .dockerignore
2025-09-05T02:28:56.3971375Z #3 transferring context: 2B done
2025-09-05T02:28:56.3973290Z #3 DONE 0.0s
2025-09-05T02:28:56.4008289Z 
2025-09-05T02:28:56.4009022Z #4 [internal] load build context
2025-09-05T02:28:56.4010249Z #4 transferring context: 9.11kB done
2025-09-05T02:28:56.4011178Z #4 DONE 0.0s
2025-09-05T02:28:56.4011718Z 
2025-09-05T02:28:56.4013626Z #5 [1/3] FROM ghcr.io/hadolint/hadolint:v2.12.0-debian@sha256:27173fe25e062448490a32de410c08491c626a0bef360aa2ce5d5bdd9384b50d
2025-09-05T02:28:56.4015634Z #5 resolve ghcr.io/hadolint/hadolint:v2.12.0-debian@sha256:27173fe25e062448490a32de410c08491c626a0bef360aa2ce5d5bdd9384b50d done
2025-09-05T02:28:56.4019316Z #5 sha256:27173fe25e062448490a32de410c08491c626a0bef360aa2ce5d5bdd9384b50d 741B / 741B done
2025-09-05T02:28:56.4020987Z #5 sha256:665ea7c2febb5533f964e122bb1f3a4d2226072269bd55139f7c00aebd464db3 740B / 740B done
2025-09-05T02:28:56.4025080Z #5 sha256:df21c2da50822cd910a7abab5f311996c4b363cdd8f61937694873326c836650 1.27kB / 1.27kB done
2025-09-05T02:28:56.4027268Z #5 sha256:e9995326b091af7b3ce352fad4d76cf3a3cb62b7a0c35cc5f625e8e649d23c50 7.34MB / 31.42MB 0.1s
2025-09-05T02:28:56.4031360Z #5 sha256:405c84fd6fe2ec3923fb21bdc293f6d8c7ee5c91d7d64a25ef2a168539cc303f 0B / 2.38MB 0.1s
2025-09-05T02:28:56.5302707Z #5 sha256:e9995326b091af7b3ce352fad4d76cf3a3cb62b7a0c35cc5f625e8e649d23c50 26.30MB / 31.42MB 0.2s
2025-09-05T02:28:56.5304541Z #5 extracting sha256:e9995326b091af7b3ce352fad4d76cf3a3cb62b7a0c35cc5f625e8e649d23c50
2025-09-05T02:28:56.6712429Z #5 sha256:e9995326b091af7b3ce352fad4d76cf3a3cb62b7a0c35cc5f625e8e649d23c50 31.42MB / 31.42MB 0.2s done
2025-09-05T02:28:56.6716618Z #5 sha256:405c84fd6fe2ec3923fb21bdc293f6d8c7ee5c91d7d64a25ef2a168539cc303f 2.38MB / 2.38MB 0.3s done
2025-09-05T02:28:58.1058659Z #5 extracting sha256:e9995326b091af7b3ce352fad4d76cf3a3cb62b7a0c35cc5f625e8e649d23c50 1.6s done
2025-09-05T02:28:58.3022222Z #5 extracting sha256:405c84fd6fe2ec3923fb21bdc293f6d8c7ee5c91d7d64a25ef2a168539cc303f 0.0s done
2025-09-05T02:28:58.4337017Z #5 DONE 2.1s
2025-09-05T02:28:58.6326146Z 
2025-09-05T02:28:58.6327682Z #6 [2/3] COPY LICENSE README.md problem-matcher.json /
2025-09-05T02:28:58.6329005Z #6 DONE 0.0s
2025-09-05T02:28:58.6329311Z 
2025-09-05T02:28:58.6329941Z #7 [3/3] COPY hadolint.sh /usr/local/bin/hadolint.sh
2025-09-05T02:28:58.6348646Z #7 DONE 0.0s
2025-09-05T02:28:58.6350456Z 
2025-09-05T02:28:58.6350683Z #8 exporting to image
2025-09-05T02:28:58.6351219Z #8 exporting layers
2025-09-05T02:28:59.0071593Z #8 exporting layers 0.5s done
2025-09-05T02:28:59.0274391Z #8 writing image sha256:3e2fbcb0a255b92486f807909bc9f3d073da39b25a88a0e60b5944478b21ce33 done
2025-09-05T02:28:59.0276732Z #8 naming to docker.io/library/f4a723:2b844dc1e4bd4f7e89a680704fb3083d done
2025-09-05T02:28:59.0305632Z #8 DONE 0.5s
2025-09-05T02:28:59.0362133Z ##[endgroup]
