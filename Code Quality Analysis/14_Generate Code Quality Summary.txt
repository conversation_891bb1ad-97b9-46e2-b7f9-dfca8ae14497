﻿2025-09-05T02:29:53.0670324Z ##[group]Run echo "## 🔍 Code Quality Results Summary" >> $GITHUB_STEP_SUMMARY
2025-09-05T02:29:53.0670916Z [36;1mecho "## 🔍 Code Quality Results Summary" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0671332Z [36;1mecho "" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0671614Z [36;1m[0m
2025-09-05T02:29:53.0671844Z [36;1mif [ "failure" == "success" ]; then[0m
2025-09-05T02:29:53.0672236Z [36;1m  echo "✅ **Quality Check**: PASSED" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0672622Z [36;1melse[0m
2025-09-05T02:29:53.0672948Z [36;1m  echo "❌ **Quality Check**: FAILED" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0673325Z [36;1mfi[0m
2025-09-05T02:29:53.0673537Z [36;1m[0m
2025-09-05T02:29:53.0673767Z [36;1mecho "" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0674166Z [36;1mecho "**Checks Performed:**" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0674686Z [36;1mecho "- Code formatting (.NET Format)" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0675234Z [36;1mecho "- Security vulnerabilities scan" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0675811Z [36;1mecho "- Dockerfile best practices" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0676333Z [36;1mecho "- Static code analysis (CodeQL)" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0676768Z [36;1mecho "" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0677309Z [36;1mecho "**Commit**: \`da24850fd7a66a23efe52054545bb9f9b0e57a84\`" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0678567Z [36;1mecho "**Workflow**: [View Details](https://github.com/HighCapitalTech/authentication-service/actions/runs/17481945030)" >> $GITHUB_STEP_SUMMARY[0m
2025-09-05T02:29:53.0708533Z shell: /usr/bin/bash -e {0}
2025-09-05T02:29:53.0708815Z env:
2025-09-05T02:29:53.0709036Z   DOTNET_VERSION: 9.0.x
2025-09-05T02:29:53.0709308Z   DOTNET_ROOT: /usr/share/dotnet
2025-09-05T02:29:53.0709607Z ##[endgroup]
