name: CI - Build and Test

on:
  pull_request:
    branches:
      - main
      - release
    types: [opened, synchronize, reopened]

env:
  DOTNET_VERSION: '9.0.x'
  PROJECT_ID: highcapital-470117
  IMAGE: authentication-service

jobs:
  build-and-test:
    name: Build, Test and Validate
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration Release --no-restore

    - name: Run unit tests
      run: |
        dotnet test tests/Domain.UnitTests/Domain.UnitTests.csproj \
          --configuration Release \
          --no-build \
          --verbosity normal \
          --logger trx \
          --results-directory TestResults/

    - name: Run application tests
      run: |
        dotnet test tests/Application.UnitTests/Application.UnitTests.csproj \
          --configuration Release \
          --no-build \
          --verbosity normal \
          --logger trx \
          --results-directory TestResults/

    - name: Publish test results
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: .NET Tests
        path: TestResults/*.trx
        reporter: dotnet-trx

    - name: Build Docker image (validation)
      run: |
        docker build -t $IMAGE:pr-${{ github.event.number }} .

    - name: Test Docker image
      run: |
        # Start container in background
        docker run -d --name test-container -p 8080:8080 $IMAGE:pr-${{ github.event.number }}
        
        # Wait for container to be ready
        sleep 30
        
        # Test if container is responding
        curl -f http://localhost:8080/swagger || exit 1
        
        # Stop and remove container
        docker stop test-container
        docker rm test-container

    - name: Comment PR with build status
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });
          
          const botComment = comments.find(comment => 
            comment.user.type === 'Bot' && comment.body.includes('🔍 CI Results')
          );
          
          const status = '${{ job.status }}';
          const emoji = status === 'success' ? '✅' : '❌';
          const statusText = status === 'success' ? 'PASSED' : 'FAILED';
          
          const body = `🔍 **CI Results for PR #${{ github.event.number }}**
          
          ${emoji} **Build Status**: ${statusText}
          
          **Details:**
          - ✅ Code compilation
          - ✅ Unit tests execution
          - ✅ Docker image build
          - ✅ Container validation
          
          **Commit**: \`${{ github.sha }}\`
          **Workflow**: [View Details](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})`;
          
          if (botComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: botComment.id,
              body: body
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: body
            });
          }
