name: Notifications

on:
  workflow_run:
    workflows: ["CI - Build and Test", "Code Quality"]
    types:
      - completed

jobs:
  notify:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request'
    
    permissions:
      contents: read

    steps:
    - name: Get PR number
      id: pr
      run: |
        PR_NUMBER=$(echo "${{ github.event.workflow_run.head_branch }}" | grep -o 'refs/pull/[0-9]\+' | grep -o '[0-9]\+' || echo "")
        if [ -z "$PR_NUMBER" ]; then
          # Fallback: try to get from head_sha
          PR_NUMBER=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/pulls?state=open&head=${{ github.repository_owner }}:${{ github.event.workflow_run.head_branch }}" \
            | jq -r '.[0].number // empty')
        fi
        echo "number=$PR_NUMBER" >> $GITHUB_OUTPUT

    - name: Create status badge
      id: badge
      run: |
        WORKFLOW_NAME="${{ github.event.workflow_run.name }}"
        STATUS="${{ github.event.workflow_run.conclusion }}"
        
        if [ "$STATUS" == "success" ]; then
          EMOJI="✅"
          COLOR="green"
        elif [ "$STATUS" == "failure" ]; then
          EMOJI="❌"
          COLOR="red"
        else
          EMOJI="⚠️"
          COLOR="yellow"
        fi
        
        echo "emoji=$EMOJI" >> $GITHUB_OUTPUT
        echo "color=$COLOR" >> $GITHUB_OUTPUT
        echo "status=$STATUS" >> $GITHUB_OUTPUT

    - name: Log workflow result
      run: |
        echo "🔔 Workflow Notification"
        echo "Workflow: ${{ github.event.workflow_run.name }}"
        echo "Status: ${{ steps.badge.outputs.emoji }} ${{ steps.badge.outputs.status }}"
        echo "PR: #${{ steps.pr.outputs.number }}"
        echo "Commit: ${{ github.event.workflow_run.head_sha }}"
        echo "Branch: ${{ github.event.workflow_run.head_branch }}"
        echo "URL: ${{ github.event.workflow_run.html_url }}"

    # Uncomment and configure if you want Slack notifications
    # - name: Slack Notification
    #   if: steps.pr.outputs.number != ''
    #   uses: 8398a7/action-slack@v3
    #   with:
    #     status: ${{ steps.badge.outputs.status }}
    #     channel: '#dev-notifications'
    #     text: |
    #       ${{ steps.badge.outputs.emoji }} **${{ github.event.workflow_run.name }}** 
    #       PR #${{ steps.pr.outputs.number }} - ${{ steps.badge.outputs.status }}
    #       <${{ github.event.workflow_run.html_url }}|View Details>
    #   env:
    #     SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    # Uncomment and configure if you want Discord notifications  
    # - name: Discord Notification
    #   if: steps.pr.outputs.number != ''
    #   uses: Ilshidur/action-discord@master
    #   with:
    #     args: |
    #       ${{ steps.badge.outputs.emoji }} **${{ github.event.workflow_run.name }}**
    #       PR #${{ steps.pr.outputs.number }} - ${{ steps.badge.outputs.status }}
    #       ${{ github.event.workflow_run.html_url }}
    #   env:
    #     DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
