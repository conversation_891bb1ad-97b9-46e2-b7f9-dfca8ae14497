name: Code Quality

on:
  pull_request:
    branches:
      - main
      - release
    types: [opened, synchronize, reopened]

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  code-analysis:
    name: Code Quality Analysis
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build for analysis
      run: dotnet build --configuration Release --no-restore

    - name: Run .NET Format check
      run: dotnet format --verify-no-changes --verbosity diagnostic

    - name: Security scan with .NET Security
      run: |
        dotnet list package --vulnerable --include-transitive 2>&1 | tee security-report.txt
        if grep -q "has the following vulnerable packages" security-report.txt; then
          echo "❌ Vulnerable packages found!"
          cat security-report.txt
          exit 1
        else
          echo "✅ No vulnerable packages found"
        fi

    - name: Dockerfile security scan
      uses: hadolint/hadolint-action@v3.1.0
      with:
        dockerfile: Dockerfile
        failure-threshold: warning

    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v3
      with:
        languages: csharp

    - name: Autobuild
      uses: github/codeql-action/autobuild@v3

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

    - name: Comment PR with quality results
      if: always()
      uses: actions/github-script@v7
      with:
        script: |
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });
          
          const botComment = comments.find(comment => 
            comment.user.type === 'Bot' && comment.body.includes('🔍 Code Quality Results')
          );
          
          const status = '${{ job.status }}';
          const emoji = status === 'success' ? '✅' : '❌';
          const statusText = status === 'success' ? 'PASSED' : 'FAILED';
          
          const body = `🔍 **Code Quality Results for PR #${{ github.event.number }}**
          
          ${emoji} **Quality Check**: ${statusText}
          
          **Checks Performed:**
          - ✅ Code formatting (.NET Format)
          - ✅ Security vulnerabilities scan
          - ✅ Dockerfile best practices
          - ✅ Static code analysis (CodeQL)
          
          **Commit**: \`${{ github.sha }}\`
          **Workflow**: [View Details](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})`;
          
          if (botComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: botComment.id,
              body: body
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: body
            });
          }
