name: Code Quality

on:
  pull_request:
    branches:
      - main
      - release
    types: [opened, synchronize, reopened]

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  code-analysis:
    name: Code Quality Analysis
    runs-on: ubuntu-latest

    permissions:
      contents: read
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build for analysis
      run: dotnet build --configuration Release --no-restore

    - name: Run .NET Format check
      run: dotnet format --verify-no-changes --verbosity diagnostic

    - name: Security scan with .NET Security
      run: |
        dotnet list package --vulnerable --include-transitive 2>&1 | tee security-report.txt
        if grep -q "has the following vulnerable packages" security-report.txt; then
          echo "❌ Vulnerable packages found!"
          cat security-report.txt
          exit 1
        else
          echo "✅ No vulnerable packages found"
        fi

    - name: Dockerfile security scan
      uses: hadolint/hadolint-action@v3.1.0
      with:
        dockerfile: Dockerfile
        failure-threshold: warning

    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v3
      with:
        languages: csharp

    - name: Autobuild
      uses: github/codeql-action/autobuild@v3

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

    - name: Generate Code Quality Summary
      if: always()
      run: |
        echo "## 🔍 Code Quality Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if [ "${{ job.status }}" == "success" ]; then
          echo "✅ **Quality Check**: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Quality Check**: FAILED" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Checks Performed:**" >> $GITHUB_STEP_SUMMARY
        echo "- Code formatting (.NET Format)" >> $GITHUB_STEP_SUMMARY
        echo "- Security vulnerabilities scan" >> $GITHUB_STEP_SUMMARY
        echo "- Dockerfile best practices" >> $GITHUB_STEP_SUMMARY
        echo "- Static code analysis (CodeQL)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Commit**: \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Workflow**: [View Details](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY

    - name: Set job status output
      if: always()
      id: status
      run: |
        echo "status=${{ job.status }}" >> $GITHUB_OUTPUT
        echo "emoji=${{ job.status == 'success' && '✅' || '❌' }}" >> $GITHUB_OUTPUT
