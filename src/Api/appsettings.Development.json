{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=postgres;Uid=high-capital-dev;Pwd=****************"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.AspNetCore.SpaProxy": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "Jwt": {"Issuer": "HighCapital.AuthenticationService", "Audience": "HighCapital.AuthenticationServiceAudience", "Secret": "CHANGE_ME_TO_A_LONG_RANDOM_SECRET_1234567890", "ExpiryMinutes": 60}, "GoogleOAuth": {"ClientId": "************-dpd7qmpieh9j32akglvd1s2r6nmjnvjd.apps.googleusercontent.com", "ClientSecret": "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", "RedirectUri": "http://localhost:5000"}}