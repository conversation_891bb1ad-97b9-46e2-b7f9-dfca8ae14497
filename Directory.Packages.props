<!-- For more info on central package management go to https://devblogs.microsoft.com/nuget/introducing-central-package-management/ -->
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <AspireVersion>9.0.0</AspireVersion>
    <AspnetVersion>9.0.0</AspnetVersion>
    <EfcoreVersion>9.0.8</EfcoreVersion>
    <MicrosoftExtensionsVersion>9.0.0</MicrosoftExtensionsVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Ardalis.GuardClauses" Version="4.6.0" />
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
    <PackageVersion Include="Azure.Identity" Version="1.13.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageVersion Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageVersion Include="HighCapital.Core" Version="1.7.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="$(AspnetVersion)" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="$(AspnetVersion)" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="$(AspnetVersion)" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="$(AspnetVersion)" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.UI" Version="$(AspnetVersion)" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="$(AspnetVersion)" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="$(EfcoreVersion)" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="$(EfcoreVersion)" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="$(EfcoreVersion)" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="$(EfcoreVersion)" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="$(EfcoreVersion)" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="$(EfcoreVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.8" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="NSwag.AspNetCore" Version="14.2.0" />
    <PackageVersion Include="NSwag.MSBuild" Version="14.2.0" />
    <PackageVersion Include="nunit" Version="3.14.0" />
    <PackageVersion Include="NUnit.Analyzers" Version="3.10.0" />
    <PackageVersion Include="NUnit3TestAdapter" Version="4.6.0" />
    <PackageVersion Include="Respawn" Version="6.2.1" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.8.1" />
    <PackageVersion Include="System.Configuration.ConfigurationManager" Version="9.0.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
    <PackageVersion Include="Microsoft.AspNetCore.SpaProxy" Version="$(AspnetVersion)" />
    <PackageVersion Include="Microsoft.Playwright" Version="1.48.0" />
    <PackageVersion Include="SpecFlow.Plus.LivingDocPlugin" Version="3.9.57" />
    <PackageVersion Include="SpecFlow.NUnit" Version="3.9.74" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="$(EfcoreVersion)" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.0.0" />
  </ItemGroup>
</Project>